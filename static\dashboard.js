// Dashboard JavaScript functionality
let currentData = null;

// Initialize dashboard on page load
document.addEventListener("DOMContentLoaded", function () {
  initializeDates();
  refreshData();
});

function initializeDates() {
  const today = new Date();
  const threeMonthsAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);

  document.getElementById("dateFin").value = today.toISOString().split("T")[0];
  document.getElementById("dateDebut").value = threeMonthsAgo
    .toISOString()
    .split("T")[0];
}

function showLoading() {
  document.getElementById("loadingIndicator").style.display = "block";
  document.getElementById("errorAlert").style.display = "none";
}

function hideLoading() {
  document.getElementById("loadingIndicator").style.display = "none";
}

function showError(message) {
  document.getElementById("errorMessage").textContent = message;
  document.getElementById("errorAlert").style.display = "block";
  hideLoading();
}

function showConnectionStatus() {
  document.getElementById("connectionStatus").style.display = "block";
}

async function refreshData() {
  showLoading();

  try {
    const dateDebut = document.getElementById("dateDebut").value;
    const dateFin = document.getElementById("dateFin").value;
    const statutFilter = document.getElementById("statutFilter").value;

    const params = new URLSearchParams();
    if (dateDebut) params.append("date_debut", dateDebut);
    if (dateFin) params.append("date_fin", dateFin);
    if (statutFilter) params.append("statut_filter", statutFilter);

    const response = await fetch(`/api/dashboard-data?${params}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      currentData = data;
      showConnectionStatus();
      renderKPIs(data.kpis);
      renderTabs();
      hideLoading();
    } else {
      throw new Error("Failed to fetch data");
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    showError("Erreur lors du chargement des données: " + error.message);
  }
}

function renderKPIs(kpis) {
  const kpiContainer = document.getElementById("kpiCards");

  const kpiCards = [
    {
      label: "Total OF Analysés",
      value: kpis.total_of,
      class: "info-card",
      icon: "fas fa-list",
    },
    {
      label: "OF en Cours",
      value: kpis.of_en_cours,
      class: "warning-card",
      icon: "fas fa-play",
    },
    {
      label: "OF Terminés",
      value: kpis.of_termines,
      class: "success-card",
      icon: "fas fa-check",
    },
    {
      label: "OF Arrêtés",
      value: kpis.of_arretes,
      class: "alert-card",
      icon: "fas fa-stop",
    },
    {
      label: "Avanc. Prod. Moyen",
      value: `${kpis.avg_prod.toFixed(1)}%`,
      class: "info-card",
      icon: "fas fa-chart-line",
    },
    {
      label: "Avanc. Temps Moyen",
      value: `${kpis.avg_temps.toFixed(1)}%`,
      class: "info-card",
      icon: "fas fa-clock",
    },
    {
      label: "⚠️ OF en Alerte Temps",
      value: kpis.alertes,
      class: "alert-card",
      icon: "fas fa-exclamation-triangle",
    },
    {
      label: "Efficacité Moyenne",
      value: kpis.efficacite.toFixed(2),
      class: "success-card",
      icon: "fas fa-tachometer-alt",
    },
  ];

  kpiContainer.innerHTML = kpiCards
    .map(
      (kpi) => `
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="metric-card ${kpi.class}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="metric-value">${kpi.value}</div>
                        <div class="metric-label">${kpi.label}</div>
                    </div>
                    <div>
                        <i class="${kpi.icon} fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    `
    )
    .join("");
}

function renderTabs() {
  const showOverview = document.getElementById("showOverview").checked;
  const showDetails = document.getElementById("showDetails").checked;
  const showCharts = document.getElementById("showCharts").checked;
  const showReport = document.getElementById("showReport").checked;
  const showCharge = document.getElementById("showCharge").checked;
  const showBacklog = document.getElementById("showBacklog").checked;
  const showPersonnel = document.getElementById("showPersonnel").checked;

  const tabs = [];
  const tabContent = [];

  if (showOverview) {
    tabs.push({ id: "overview", label: "📊 Synthèse", active: true });
    tabContent.push({ id: "overview", content: renderOverviewTab() });
  }

  if (showDetails && currentData.data.main_of.length > 0) {
    tabs.push({ id: "details", label: "📋 Détail OF", active: !showOverview });
    tabContent.push({ id: "details", content: renderDetailsTab() });
  }

  if (showCharts && currentData.data.main_of.length > 0) {
    tabs.push({
      id: "charts",
      label: "📈 Graphiques",
      active: !showOverview && !showDetails,
    });
    tabContent.push({ id: "charts", content: renderChartsTab() });
  }

  if (showReport && currentData.data.main_of.length > 0) {
    tabs.push({ id: "report", label: "📄 Rapport", active: false });
    tabContent.push({ id: "report", content: renderReportTab() });
  }

  if (showCharge && currentData.data.charge.length > 0) {
    tabs.push({ id: "charge", label: "⚙️ Charge Travail", active: false });
    tabContent.push({ id: "charge", content: renderChargeTab() });
  }

  if (showBacklog && currentData.data.backlog.length > 0) {
    tabs.push({ id: "backlog", label: "⏳ Backlog", active: false });
    tabContent.push({ id: "backlog", content: renderBacklogTab() });
  }

  if (showPersonnel && currentData.data.personnel.length > 0) {
    tabs.push({ id: "personnel", label: "👥 Personnel", active: false });
    tabContent.push({ id: "personnel", content: renderPersonnelTab() });
  }

  // Render tabs
  const tabsContainer = document.getElementById("mainTabs");
  tabsContainer.innerHTML = tabs
    .map(
      (tab) => `
        <li class="nav-item" role="presentation">
            <button class="nav-link ${tab.active ? "active" : ""}" 
                    id="${tab.id}-tab" 
                    data-bs-toggle="tab" 
                    data-bs-target="#${tab.id}" 
                    type="button" 
                    role="tab">
                ${tab.label}
            </button>
        </li>
    `
    )
    .join("");

  // Render tab content
  const contentContainer = document.getElementById("mainTabContent");
  contentContainer.innerHTML = tabContent
    .map(
      (content) => `
        <div class="tab-pane fade ${
          content.id === tabs.find((t) => t.active)?.id ? "show active" : ""
        }" 
             id="${content.id}" 
             role="tabpanel">
            ${content.content}
        </div>
    `
    )
    .join("");

  // Initialize charts after content is rendered
  setTimeout(() => {
    if (showCharts && currentData.data.main_of.length > 0) {
      initializeCharts();
    }
  }, 100);
}

function renderOverviewTab() {
  const data = currentData.data.main_of;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée OF disponible pour la synthèse.</div>';
  }

  return `
        <div class="mt-3">
            <h4>📊 Synthèse Globale de la Production</h4>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div id="statusPieChart" style="height: 400px;"></div>
                </div>
                <div class="col-md-6">
                    <div id="alertsBarChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    `;
}

function renderDetailsTab() {
  const data = currentData.data.main_of;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée OF disponible.</div>';
  }

  const tableRows = data
    .slice(0, 100)
    .map(
      (row) => `
        <tr>
            <td>${row.NUMERO_OFDA || ""}</td>
            <td>${row.PRODUIT || ""}</td>
            <td>${row.DESIGNATION || ""}</td>
            <td><span class="badge bg-${getStatusBadgeClass(row.STATUT)}">${
        row.STATUT || ""
      }</span></td>
            <td>${row.CLIENT || ""}</td>
            <td>${row.FAMILLE_TECHNIQUE || ""}</td>
            <td>${row.LANCEMENT_AU_PLUS_TARD || ""}</td>
            <td>${row.QUANTITE_DEMANDEE || ""}</td>
            <td>${row.CUMUL_ENTREES || ""}</td>
            <td>${formatPercentage(row.Avancement_PROD)}</td>
            <td>${row.DUREE_PREVUE || ""}</td>
            <td>${row.CUMUL_TEMPS_PASSES || ""}</td>
            <td>${formatPercentage(row.Avancement_temps)}</td>
            <td>${
              row.EFFICACITE ? parseFloat(row.EFFICACITE).toFixed(2) : ""
            }</td>
            <td>${row.Alerte_temps ? "⚠️ Oui" : "✅ Non"}</td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>📋 Données Détaillées des Ordres de Fabrication</h4>
            <p class="text-muted">Affichage des 100 premiers enregistrements</p>
            <div class="table-container">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>N° OF</th>
                            <th>Produit</th>
                            <th>Désignation</th>
                            <th>Statut</th>
                            <th>Client</th>
                            <th>Famille</th>
                            <th>Lancement</th>
                            <th>Qté Demandée</th>
                            <th>Qté Produite</th>
                            <th>Avanc. Prod.</th>
                            <th>Durée Prévue</th>
                            <th>Temps Passé</th>
                            <th>Avanc. Temps</th>
                            <th>Efficacité</th>
                            <th>Alerte</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function renderChartsTab() {
  return `
        <div class="mt-3">
            <h4>📈 Analyse Graphique de la Production</h4>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div id="scatterChart" style="height: 400px;"></div>
                </div>
                <div class="col-md-6">
                    <div id="efficiencyChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    `;
}

function renderReportTab() {
  return `
        <div class="mt-3">
            <h4>📄 Rapport Textuel de Synthèse</h4>
            <div class="d-flex justify-content-end mb-3">
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-file-alt"></i> Générer le Rapport
                </button>
            </div>
            <div id="reportContent">
                <div class="alert alert-info">Cliquez sur "Générer le Rapport" pour créer le rapport de synthèse.</div>
            </div>
        </div>
    `;
}

function renderChargeTab() {
  const data = currentData.data.charge;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée de charge de travail disponible.</div>';
  }

  const tableRows = data
    .map(
      (row) => `
        <tr>
            <td>${row.SECTEUR || ""}</td>
            <td>${row.NB_OPERATEURS || 0}</td>
            <td>${row.NB_APPRENTIS || 0}</td>
            <td>${row.NB_HEURES_DISPONIBLES_SEMAINE || 0}h</td>
            <td>${row.HEURES_PLANIFIEES || 0}h</td>
            <td>${row.NB_OF_EN_COURS || 0}</td>
            <td><span class="badge bg-${getChargeBadgeClass(
              row.TAUX_CHARGE_POURCENT
            )}">${
        row.TAUX_CHARGE_POURCENT
          ? parseFloat(row.TAUX_CHARGE_POURCENT).toFixed(1)
          : 0
      }%</span></td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>⚙️ Analyse de la Charge de Travail</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Secteur</th>
                            <th>Opérateurs</th>
                            <th>Apprentis</th>
                            <th>Heures Dispo./Sem.</th>
                            <th>Heures Planifiées</th>
                            <th>OF en Cours</th>
                            <th>Taux de Charge</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function renderBacklogTab() {
  const data = currentData.data.backlog;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée de backlog disponible.</div>';
  }

  const tableRows = data
    .map(
      (row) => `
        <tr class="${getPriorityRowClass(row.PRIORITE)}">
            <td>${row.NUMERO_OFDA || ""}</td>
            <td>${row.PRODUIT || ""}</td>
            <td>${row.DESIGNATION || ""}</td>
            <td>${row.CLIENT || ""}</td>
            <td>${row.QUANTITE_RESTANTE || 0}</td>
            <td>${row.LANCEMENT_AU_PLUS_TARD || ""}</td>
            <td>${row.RETARD_JOURS || 0} j</td>
            <td><span class="badge bg-${getPriorityBadgeClass(row.PRIORITE)}">${
        row.PRIORITE || ""
      }</span></td>
            <td>${
              row.TEMPS_RESTANT_ESTIME
                ? parseFloat(row.TEMPS_RESTANT_ESTIME).toFixed(1)
                : 0
            }h</td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>⏳ Analyse du Backlog de Production</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>N° OF</th>
                            <th>Produit</th>
                            <th>Désignation</th>
                            <th>Client</th>
                            <th>Qté Restante</th>
                            <th>Lancement Prévu</th>
                            <th>Retard</th>
                            <th>Priorité</th>
                            <th>Temps Restant</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

function renderPersonnelTab() {
  const data = currentData.data.personnel;

  if (data.length === 0) {
    return '<div class="alert alert-info">Aucune donnée de personnel disponible.</div>';
  }

  const tableRows = data
    .map(
      (row) => `
        <tr>
            <td>${row.NOM_COMPLET || ""}</td>
            <td>${row.QUALIFICATION || ""}</td>
            <td>${row.SECTEUR_PERSONNEL || ""}</td>
            <td>${
              row.COEFFICIENT_EFFICACITE
                ? parseFloat(row.COEFFICIENT_EFFICACITE).toFixed(2)
                : ""
            }</td>
            <td><span class="badge bg-success">Actif</span></td>
        </tr>
    `
    )
    .join("");

  return `
        <div class="mt-3">
            <h4>👥 Analyse du Personnel Actif</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Nom Complet</th>
                            <th>Qualification</th>
                            <th>Secteur</th>
                            <th>Coeff. Efficacité</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

// Utility functions
function formatPercentage(value) {
  if (value === null || value === undefined || value === "") return "";
  return (parseFloat(value) * 100).toFixed(1) + "%";
}

function getStatusBadgeClass(status) {
  switch (status) {
    case "C":
      return "warning";
    case "T":
      return "success";
    case "A":
      return "danger";
    default:
      return "secondary";
  }
}

function getChargeBadgeClass(percentage) {
  const pct = parseFloat(percentage) || 0;
  if (pct > 100) return "danger";
  if (pct > 80) return "warning";
  if (pct > 60) return "info";
  return "success";
}

function getPriorityBadgeClass(priority) {
  switch (priority) {
    case "URGENT":
      return "danger";
    case "PRIORITAIRE":
      return "warning";
    case "NORMAL":
      return "success";
    default:
      return "secondary";
  }
}

function getPriorityRowClass(priority) {
  switch (priority) {
    case "URGENT":
      return "table-danger";
    case "PRIORITAIRE":
      return "table-warning";
    default:
      return "";
  }
}

// Chart initialization functions
function initializeCharts() {
  if (currentData && currentData.data.main_of.length > 0) {
    createStatusPieChart();
    createAlertsBarChart();
    createScatterChart();
    createEfficiencyChart();
  }
}

function createStatusPieChart() {
  const data = currentData.data.main_of;
  const statusCounts = {};

  data.forEach((row) => {
    const status = row.STATUT || "Unknown";
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  const labels = Object.keys(statusCounts);
  const values = Object.values(statusCounts);

  const plotData = [
    {
      values: values,
      labels: labels,
      type: "pie",
      hole: 0.3,
      textinfo: "label+percent",
      textposition: "inside",
    },
  ];

  const layout = {
    title: "Répartition des Statuts OF",
    showlegend: true,
    height: 400,
  };

  Plotly.newPlot("statusPieChart", plotData, layout);
}

function createAlertsBarChart() {
  const data = currentData.data.main_of;
  const alertCounts = { Normal: 0, "En Alerte": 0 };

  data.forEach((row) => {
    if (row.Alerte_temps) {
      alertCounts["En Alerte"]++;
    } else {
      alertCounts["Normal"]++;
    }
  });

  const plotData = [
    {
      x: Object.keys(alertCounts),
      y: Object.values(alertCounts),
      type: "bar",
      marker: {
        color: ["mediumseagreen", "orangered"],
      },
    },
  ];

  const layout = {
    title: "Alertes Temps vs Normaux",
    xaxis: { title: "Statut Alerte" },
    yaxis: { title: "Nombre OF" },
    height: 400,
  };

  Plotly.newPlot("alertsBarChart", plotData, layout);
}

function createScatterChart() {
  const data = currentData.data.main_of;

  const x = data.map((row) => parseFloat(row.Avancement_PROD) || 0);
  const y = data.map((row) => parseFloat(row.Avancement_temps) || 0);
  const text = data.map(
    (row) =>
      `${row.NUMERO_OFDA}<br>${row.PRODUIT}<br>Efficacité: ${
        row.EFFICACITE || "N/A"
      }`
  );
  const colors = data.map((row) => row.STATUT);

  const plotData = [
    {
      x: x,
      y: y,
      mode: "markers",
      type: "scatter",
      text: text,
      hovertemplate: "%{text}<extra></extra>",
      marker: {
        size: 8,
        color: colors,
        colorscale: "Viridis",
      },
    },
  ];

  // Add reference line
  plotData.push({
    x: [0, 1],
    y: [0, 1],
    mode: "lines",
    type: "scatter",
    line: { color: "red", dash: "dash" },
    showlegend: false,
    hoverinfo: "skip",
  });

  const layout = {
    title: "Corrélation Avancement Production et Temps",
    xaxis: {
      title: "Avancement Production (%)",
      tickformat: ".0%",
    },
    yaxis: {
      title: "Avancement Temps (%)",
      tickformat: ".0%",
    },
    height: 400,
  };

  Plotly.newPlot("scatterChart", plotData, layout);
}

function createEfficiencyChart() {
  const data = currentData.data.main_of;
  const efficiencyValues = data
    .map((row) => parseFloat(row.EFFICACITE))
    .filter((val) => val > 0 && val < 5); // Filter out extreme values

  const plotData = [
    {
      x: efficiencyValues,
      type: "histogram",
      nbinsx: 30,
      marker: { color: "skyblue" },
    },
  ];

  const layout = {
    title: "Distribution de l'Efficacité des OF",
    xaxis: { title: "Efficacité (Prévu/Passé)" },
    yaxis: { title: "Nombre OF" },
    height: 400,
  };

  Plotly.newPlot("efficiencyChart", plotData, layout);
}

// Report generation
async function generateReport() {
  try {
    const dateDebut = document.getElementById("dateDebut").value;
    const dateFin = document.getElementById("dateFin").value;
    const statutFilter = document.getElementById("statutFilter").value;

    const params = new URLSearchParams();
    if (dateDebut) params.append("date_debut", dateDebut);
    if (dateFin) params.append("date_fin", dateFin);
    if (statutFilter) params.append("statut_filter", statutFilter);

    const response = await fetch(`/api/report?${params}`);
    const data = await response.json();

    if (data.success) {
      document.getElementById("reportContent").innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <pre style="white-space: pre-wrap; font-family: monospace;">${data.report}</pre>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success" onclick="downloadReport('${data.report}')">
                        <i class="fas fa-download"></i> Télécharger le Rapport
                    </button>
                </div>
            `;
    } else {
      throw new Error("Failed to generate report");
    }
  } catch (error) {
    console.error("Error generating report:", error);
    document.getElementById("reportContent").innerHTML = `
            <div class="alert alert-danger">
                Erreur lors de la génération du rapport: ${error.message}
            </div>
        `;
  }
}

function downloadReport(reportText) {
  const blob = new Blob([reportText], { type: "text/plain" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `rapport_production_${
    new Date().toISOString().split("T")[0]
  }.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}
