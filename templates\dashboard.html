<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Suivi de Production - Excalibur ERP</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .sidebar {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }
        .main-content {
            padding: 20px;
        }
        .alert-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .success-card {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }
        .warning-card {
            background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
        }
        .info-card {
            background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .table-container {
            max-height: 500px;
            overflow-y: auto;
        }
        .nav-tabs .nav-link.active {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar">
                <h4 class="mb-4">
                    <i class="fas fa-cogs"></i> Filtres & Options
                </h4>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="fas fa-calendar"></i> Période d'Analyse
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="dateDebut" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="dateDebut">
                        </div>
                        <div class="mb-3">
                            <label for="dateFin" class="form-label">Date de fin</label>
                            <input type="date" class="form-control" id="dateFin">
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="fas fa-filter"></i> Filtre Statut OF
                    </div>
                    <div class="card-body">
                        <select class="form-select" id="statutFilter">
                            <option value="">Tous</option>
                            <option value="C">En Cours (C)</option>
                            <option value="T">Terminés (T)</option>
                            <option value="A">Arrêtés (A)</option>
                        </select>
                    </div>
                </div>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <i class="fas fa-eye"></i> Sections à Afficher
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showOverview" checked>
                            <label class="form-check-label" for="showOverview">Synthèse Dashboard</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showDetails" checked>
                            <label class="form-check-label" for="showDetails">Détail OF</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showCharts" checked>
                            <label class="form-check-label" for="showCharts">Graphiques</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showReport">
                            <label class="form-check-label" for="showReport">Rapport</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showCharge">
                            <label class="form-check-label" for="showCharge">Charge Travail</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showBacklog">
                            <label class="form-check-label" for="showBacklog">Backlog</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showPersonnel">
                            <label class="form-check-label" for="showPersonnel">Personnel</label>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-primary w-100 mb-3" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Actualiser les Données
                </button>
                
                <div class="alert alert-info">
                    <strong>Aide Rapide:</strong><br>
                    • <strong>Avancement Prod</strong>: % Quantité produite / demandée<br>
                    • <strong>Avancement Temps</strong>: % Temps passé / prévu<br>
                    • <strong>⚠️ Alerte</strong>: OF dont le temps passé > temps prévu<br>
                    • <strong>Efficacité</strong>: Ratio Temps Prévu / Temps Passé
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 main-content">
                <h1 class="mb-4">
                    🏭 Suivi de Production - Excalibur ERP
                </h1>
                <p class="text-muted">Tableau de bord pour l'analyse des performances et de la charge de travail</p>
                <hr>
                
                <!-- Loading indicator -->
                <div class="loading" id="loadingIndicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p>Chargement des données...</p>
                </div>
                
                <!-- Connection Status -->
                <div class="alert alert-success" id="connectionStatus" style="display: none;">
                    <i class="fas fa-check-circle"></i> Connecté à Excalibur ERP
                </div>
                
                <!-- Error Alert -->
                <div class="alert alert-danger" id="errorAlert" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span id="errorMessage"></span>
                </div>
                
                <!-- KPI Cards -->
                <div class="row" id="kpiCards">
                    <!-- KPI cards will be populated by JavaScript -->
                </div>
                
                <!-- Tabs -->
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <!-- Tabs will be populated by JavaScript -->
                </ul>
                
                <div class="tab-content" id="mainTabContent">
                    <!-- Tab content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/static/dashboard.js"></script>
</body>
</html>
